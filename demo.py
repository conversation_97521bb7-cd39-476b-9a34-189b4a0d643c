#!/usr/bin/env python3
"""
DailyMed高级搜索工具演示脚本
展示如何搜索含特定非活性成分的注射剂
"""

import subprocess
import sys
import os
from datetime import datetime

def run_demo_search(search_term, max_drugs=30):
    """运行演示搜索"""
    print(f"\n{'='*80}")
    print(f"🔍 搜索含 {search_term} 的注射剂（前{max_drugs}个药品）")
    print(f"{'='*80}")
    
    # 生成输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"{search_term.lower().replace(' ', '_')}_injections_{timestamp}.csv"
    
    # 构建命令
    command = [
        "python3", "dailymed_advanced_search.py",
        "--search-type", "INACTIVE_INGREDIENT",
        "--search-term", search_term,
        "--max-drugs", str(max_drugs),
        "--output", output_file
    ]
    
    print(f"执行命令: {' '.join(command)}")
    print(f"输出文件: {output_file}")
    print("-" * 80)
    
    try:
        # 运行搜索
        result = subprocess.run(command, capture_output=True, text=True, timeout=180)
        
        if result.returncode == 0:
            # 解析输出，提取关键信息
            lines = result.stdout.strip().split('\n')
            
            total_checked = 0
            total_found = 0
            
            for line in lines:
                if "总共检查了" in line and "个药品" in line:
                    try:
                        total_checked = int(line.split("总共检查了")[1].split("个药品")[0].strip())
                    except:
                        pass
                
                if "找到" in line and "个注射剂" in line:
                    try:
                        total_found = int(line.split("找到")[1].split("个注射剂")[0].strip())
                    except:
                        pass
            
            print(f"✅ 搜索完成！")
            print(f"📊 检查了 {total_checked} 个药品")
            print(f"💉 找到 {total_found} 个注射剂")
            
            # 显示找到的注射剂
            if total_found > 0:
                print(f"\n🎯 找到的注射剂:")
                in_results_section = False
                count = 0
                
                for line in lines:
                    if "=== 找到的注射剂" in line:
                        in_results_section = True
                        continue
                    elif "=== 搜索完成 ===" in line:
                        in_results_section = False
                        break
                    elif in_results_section and line.strip():
                        if line.strip().startswith(("1.", "2.", "3.", "4.", "5.")):
                            print(f"  {line.strip()}")
                            count += 1
                        elif "关键词:" in line:
                            print(f"    {line.strip()}")
                        elif "剂型:" in line:
                            print(f"    {line.strip()}")
                        
                        if count >= 5:  # 只显示前5个
                            if total_found > 5:
                                print(f"    ... 还有 {total_found - 5} 个结果")
                            break
            
            # 检查文件是否生成
            if os.path.exists(output_file):
                print(f"\n📄 结果已保存到: {output_file}")
                return output_file
            else:
                print(f"\n⚠️  输出文件未生成")
                return None
                
        else:
            print(f"❌ 搜索失败:")
            print(result.stderr)
            return None
            
    except subprocess.TimeoutExpired:
        print("⏰ 搜索超时")
        return None
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        return None

def show_csv_summary(csv_file):
    """显示CSV文件摘要"""
    if not csv_file or not os.path.exists(csv_file):
        return
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) <= 1:
            print("📄 CSV文件为空")
            return
        
        print(f"\n📋 CSV文件摘要 ({csv_file}):")
        print(f"   总记录数: {len(lines) - 1}")
        print(f"   文件大小: {os.path.getsize(csv_file)} 字节")
        
        # 显示前几行
        print(f"\n📝 前3条记录:")
        for i, line in enumerate(lines[1:4], 1):  # 跳过标题行
            if line.strip():
                parts = line.split(',')
                if len(parts) >= 2:
                    name = parts[0].strip('"')
                    print(f"   {i}. {name}")
        
    except Exception as e:
        print(f"❌ 读取CSV文件失败: {e}")

def main():
    """主演示函数"""
    print("🧪 DailyMed高级搜索工具演示")
    print("=" * 80)
    print("本演示将搜索DailyMed数据库中含特定非活性成分的注射剂药品")
    print()
    
    # 检查脚本是否存在
    if not os.path.exists("dailymed_advanced_search.py"):
        print("❌ 找不到 dailymed_advanced_search.py 脚本")
        print("请确保该文件在当前目录中")
        return
    
    # 演示搜索列表
    demo_searches = [
        ("ALCOHOL", 50),           # 酒精
        ("BENZYL ALCOHOL", 30),    # 苯甲醇
        ("PROPYLENE GLYCOL", 30),  # 丙二醇
    ]
    
    results = []
    
    for search_term, max_drugs in demo_searches:
        csv_file = run_demo_search(search_term, max_drugs)
        if csv_file:
            results.append((search_term, csv_file))
            show_csv_summary(csv_file)
        
        print()  # 空行分隔
    
    # 最终总结
    print("🎉 演示完成！")
    print("=" * 80)
    
    if results:
        print("📁 生成的文件:")
        for search_term, csv_file in results:
            print(f"   • {search_term}: {csv_file}")
        
        print("\n💡 使用提示:")
        print("   • 可以用Excel或其他工具打开CSV文件")
        print("   • 每个文件包含药品名称、ID、URL、剂型等详细信息")
        print("   • URL可以直接访问查看药品详情")
        print("   • 可以根据需要调整搜索参数（--max-drugs, --search-term等）")
    else:
        print("⚠️  没有生成任何结果文件")
    
    print("\n📖 更多用法请参考 README.md 文件")

if __name__ == "__main__":
    main()
