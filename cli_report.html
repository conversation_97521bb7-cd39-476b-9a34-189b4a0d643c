
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DailyMed搜索结果报告</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .chart-container {
            margin: 30px 0;
            text-align: center;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .keywords-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .keyword-tag {
            background-color: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
        }
        .section {
            margin: 30px 0;
        }
        .section h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
    </style>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        
</head>
<body>
    <div class="container">
        <h1>DailyMed搜索结果分析报告</h1>
        
        <div class="section">
            <h2>📊 统计概览</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">2</div>
                    <div class="stat-label">总药品数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">2</div>
                    <div class="stat-label">注射剂数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">2</div>
                    <div class="stat-label">高置信度</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100.0%</div>
                    <div class="stat-label">搜索词验证率</div>
                </div>
            </div>
        </div>
        
        
        <div class="section">
            <h2>📈 数据可视化</h2>
            <div class="chart-container">
                <canvas id="confidenceChart" width="400" height="200"></canvas>
            </div>
            <script>
                const ctx = document.getElementById('confidenceChart').getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['高置信度', '中置信度', '低置信度'],
                        datasets: [{
                            data: [2, 0, 0],
                            backgroundColor: ['#2ecc71', '#f39c12', '#e74c3c']
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: '置信度分布'
                            }
                        }
                    }
                });
            </script>
        </div>
        
        
        <div class="section">
            <h2>🔍 常见注射剂关键词</h2>
            <div class="keywords-list">
                <span class="keyword-tag">intravenous (2)</span><span class="keyword-tag">intravenously (2)</span><span class="keyword-tag">iv (2)</span><span class="keyword-tag">im (2)</span><span class="keyword-tag">sc (2)</span><span class="keyword-tag">sq (2)</span><span class="keyword-tag">inject (2)</span><span class="keyword-tag">injection (2)</span><span class="keyword-tag">injectable (2)</span><span class="keyword-tag">parenteral (2)</span>
            </div>
        </div>
        
        <div class="section">
            <h2>📋 详细数据</h2>
            <table class="data-table"><thead><tr><th>药品名称</th><th>置信度</th><th>是否注射剂</th><th>搜索词验证</th></tr></thead><tbody><tr><td>GLYCOPYRROLATE INJECTION, SOLUTION</td><td>高</td><td>是</td><td>是</td></tr><tr><td>LUPRON (LEUPROLIDE ACETATE) LUPRON (LEUPROLIDE ACETATE) INJECTION, SOLUTION</td><td>高</td><td>是</td><td>是</td></tr></tbody></table>
        </div>
        
        <div class="section">
            <p style="text-align: center; color: #7f8c8d; font-size: 0.9em;">
                报告生成时间: 2025-09-02 14:58:35
            </p>
        </div>
    </div>
</body>
</html>
        