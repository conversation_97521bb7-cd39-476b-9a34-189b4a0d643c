# DailyMed高级搜索和注射剂过滤工具

这是一个用于搜索DailyMed数据库并过滤出注射剂药品的Python工具。

## 功能特点

- 🔍 支持DailyMed高级搜索功能
- 💉 智能识别和过滤注射剂
- 📊 导出结果到CSV文件
- 🎯 支持多种搜索类型（活性成分、非活性成分等）
- ⚡ 可配置搜索范围和速度
- 📝 详细的日志输出

## 安装要求

- Python 3.6+
- 标准库（无需额外安装包）

## 使用方法

### 基本用法

```bash
python3 dailymed_advanced_search.py --search-type INACTIVE_INGREDIENT --search-term ALCOHOL
```

### 完整参数说明

```bash
python3 dailymed_advanced_search.py [选项]

选项:
  --search-type SEARCH_TYPE    搜索类型 (默认: INACTIVE_INGREDIENT)
  --search-term SEARCH_TERM    搜索词 (默认: ALCOHOL)
  --label-type LABEL_TYPE      标签类型 (默认: human)
  --max-pages MAX_PAGES        最大搜索页数
  --max-drugs MAX_DRUGS        最大检查药品数
  --output OUTPUT              输出CSV文件名
  --delay DELAY                请求间隔时间(秒) (默认: 0.5)
```

### 常用搜索类型

1. **INACTIVE_INGREDIENT** - 非活性成分（辅料）
2. **ACTIVE_INGREDIENT** - 活性成分
3. **DRUG_NAME** - 药品名称
4. **MANUFACTURER** - 制造商

### 使用示例

#### 1. 搜索含酒精的注射剂

```bash
python3 dailymed_advanced_search.py \
  --search-type INACTIVE_INGREDIENT \
  --search-term ALCOHOL \
  --max-drugs 100 \
  --output alcohol_injections.csv
```

#### 2. 搜索含苯甲醇的注射剂

```bash
python3 dailymed_advanced_search.py \
  --search-type INACTIVE_INGREDIENT \
  --search-term "BENZYL ALCOHOL" \
  --max-drugs 50 \
  --output benzyl_alcohol_injections.csv
```

#### 3. 搜索含胰岛素的注射剂

```bash
python3 dailymed_advanced_search.py \
  --search-type ACTIVE_INGREDIENT \
  --search-term INSULIN \
  --max-drugs 30 \
  --output insulin_injections.csv
```

#### 4. 搜索特定制造商的注射剂

```bash
python3 dailymed_advanced_search.py \
  --search-type MANUFACTURER \
  --search-term "PFIZER" \
  --max-drugs 50 \
  --output pfizer_injections.csv
```

## 注射剂识别逻辑

工具使用以下逻辑来识别注射剂：

### 强注射剂指示词（药品名称中）
- injection, injectable
- intravenous, intramuscular, subcutaneous

### 弱注射剂指示词（药品名称中）
- solution, vial, ampule, ampoule

### 内容关键词
- iv, im, sc, sq
- bolus, infusion, parenteral
- sterile solution, sterile suspension
- single-dose vial, multi-dose vial

### 排除词汇
- tablet, capsule, cream, ointment
- gel, lotion, syrup, liquid

### 判断规则
1. 如果药品名称包含强注射剂指示词且无排除词 → 注射剂
2. 如果药品名称包含弱注射剂指示词且无排除词且内容有注射剂关键词 → 注射剂
3. 其他情况 → 非注射剂

## 输出文件格式

生成的CSV文件包含以下列：

| 列名 | 描述 |
|------|------|
| name | 药品名称 |
| setid | 药品唯一标识符 |
| url | 药品详情页面URL |
| title | 完整标题 |
| dosage_form | 剂型 |
| is_injection | 是否为注射剂 (True/False) |
| injection_keywords | 找到的注射剂关键词 |
| exclusion_keywords | 找到的排除关键词 |

## 性能优化建议

1. **控制搜索范围**: 使用 `--max-drugs` 限制检查的药品数量
2. **调整请求间隔**: 使用 `--delay` 设置请求间隔，避免被服务器限制
3. **分批处理**: 对于大量数据，建议分批次处理

## 示例脚本

运行 `usage_examples.py` 查看更多使用示例：

```bash
python3 usage_examples.py
```

## 注意事项

1. **请求频率**: 工具默认在每个请求之间等待0.5秒，避免对DailyMed服务器造成过大压力
2. **网络连接**: 需要稳定的网络连接访问DailyMed网站
3. **数据准确性**: 注射剂识别基于关键词匹配，可能存在误判，建议人工复核重要结果
4. **法律合规**: 请遵守DailyMed的使用条款和相关法律法规

## 故障排除

### 常见问题

1. **连接超时**: 检查网络连接，可以增加 `--delay` 参数
2. **没有找到注射剂**: 尝试增加 `--max-drugs` 参数扩大搜索范围
3. **搜索结果为空**: 检查搜索词拼写，尝试使用不同的搜索类型

### 调试模式

如需查看详细的调试信息，可以修改脚本中的日志级别：

```python
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
```

## 许可证

本工具仅供学习和研究使用。使用时请遵守相关法律法规和DailyMed的使用条款。

## 更新日志

- v1.0: 初始版本，支持基本的搜索和过滤功能
- v1.1: 改进注射剂识别逻辑，提高准确性
- v1.2: 添加更多搜索类型支持，优化性能
