#!/usr/bin/env python3
"""
DailyMed高级搜索脚本
支持自定义搜索条件和注射剂过滤
"""

import urllib.request
import urllib.parse
import json
import time
import re
import html
import logging
import csv
import argparse
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DailyMedAdvancedSearcher:
    def __init__(self):
        self.base_web_url = "https://dailymed.nlm.nih.gov/dailymed"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # 注射剂相关关键词（更精确的匹配）
        self.injection_keywords = [
            # 明确的注射剂词汇
            'injection', 'injectable', 'intravenous', 'intramuscular', 'subcutaneous',
            'solution for injection', 'suspension for injection', 'powder for injection',
            'concentrate for injection', 'emulsion for injection',
            
            # 给药途径缩写
            'iv', 'im', 'sc', 'sq',
            
            # 包装形式
            'vial', 'ampule', 'ampoule', 'prefilled syringe', 'auto-injector',
            'single-dose vial', 'multi-dose vial',
            
            # 给药方式
            'bolus', 'infusion', 'parenteral', 'intravenously', 'intramuscularly',
            
            # 剂型描述
            'sterile solution', 'sterile suspension', 'lyophilized powder'
        ]
        
        # 排除的关键词（避免误判）
        self.exclusion_keywords = [
            'oral', 'tablet', 'capsule', 'cream', 'ointment', 'gel', 'lotion',
            'topical', 'nasal', 'ophthalmic', 'otic', 'rectal', 'vaginal',
            'transdermal', 'sublingual', 'buccal'
        ]
    
    def _make_request(self, url):
        """发送HTTP请求"""
        req = urllib.request.Request(url, headers=self.headers)
        try:
            with urllib.request.urlopen(req, timeout=30) as response:
                return response.read().decode('utf-8')
        except Exception as e:
            logger.error(f"请求失败 {url}: {e}")
            return None
    
    def build_search_url(self, search_type="INACTIVE_INGREDIENT", search_term="ALCOHOL", 
                        label_type="human", page=1, pagesize=50):
        """构建高级搜索URL"""
        # 构建查询字符串
        if search_type and search_term:
            query = f"{search_type}:({search_term})"
        else:
            query = search_term
        
        search_params = {
            'adv': '1',
            'labeltype': label_type,
            'query': query,
            'page': page,
            'pagesize': pagesize
        }
        
        url = f"{self.base_web_url}/search.cfm?" + urllib.parse.urlencode(search_params)
        return url
    
    def search_drugs(self, search_type="INACTIVE_INGREDIENT", search_term="ALCOHOL", 
                    label_type="human", page=1, pagesize=50):
        """执行高级搜索"""
        try:
            url = self.build_search_url(search_type, search_term, label_type, page, pagesize)
            logger.info(f"搜索URL: {url}")
            
            response_text = self._make_request(url)
            if not response_text:
                return [], 0
            
            # 解析搜索结果
            results = []
            
            # 查找包含setid的链接
            setid_pattern = r'/dailymed/drugInfo\.cfm\?setid=([a-f0-9-]+)'
            setid_matches = re.findall(setid_pattern, response_text)
            
            # 查找药品名称
            name_pattern = r'<a[^>]*href="[^"]*setid=[a-f0-9-]+"[^>]*>([^<]+)</a>'
            name_matches = re.findall(name_pattern, response_text, re.IGNORECASE)
            
            # 组合结果
            for i, setid in enumerate(setid_matches):
                name = html.unescape(name_matches[i].strip()) if i < len(name_matches) else f"Drug_{i+1}"
                results.append({
                    'name': name,
                    'setid': setid,
                    'url': f"{self.base_web_url}/drugInfo.cfm?setid={setid}"
                })
            
            # 获取总结果数
            total_results = 0
            # 尝试多种模式匹配总数
            patterns = [
                r'(\d+)\s+results',
                r'of\s+(\d+)\s+',
                r'(\d+)\s+of\s+\d+',
                r'first\s+\d+\s+of\s+(\d+)'
            ]
            
            for pattern in patterns:
                result_match = re.search(pattern, response_text, re.IGNORECASE)
                if result_match:
                    total_results = int(result_match.group(1))
                    break
            
            logger.info(f"页面 {page}: 找到 {len(results)} 个药品，总计 {total_results} 个结果")
            return results, total_results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return [], 0
    
    def get_drug_details(self, setid):
        """获取药品详细信息"""
        try:
            url = f"{self.base_web_url}/drugInfo.cfm?setid={setid}"
            response_text = self._make_request(url)
            if not response_text:
                return None
            
            info = {'setid': setid}
            
            # 提取药品名称
            title_match = re.search(r'<title>([^<]+)</title>', response_text, re.IGNORECASE)
            if title_match:
                info['title'] = html.unescape(title_match.group(1).strip())
            
            # 提取剂型信息
            dosage_patterns = [
                r'dosage\s+form[^:]*:\s*([^<\n]+)',
                r'pharmaceutical\s+form[^:]*:\s*([^<\n]+)',
                r'<strong[^>]*>([^<]*(?:injection|solution|suspension|tablet|capsule|cream)[^<]*)</strong>'
            ]
            
            for pattern in dosage_patterns:
                dosage_match = re.search(pattern, response_text, re.IGNORECASE)
                if dosage_match:
                    info['dosage_form'] = html.unescape(dosage_match.group(1).strip())
                    break
            
            # 检查是否包含注射剂关键词
            text_lower = response_text.lower()
            injection_found = []
            exclusion_found = []
            
            for keyword in self.injection_keywords:
                if keyword.lower() in text_lower:
                    injection_found.append(keyword)
            
            for keyword in self.exclusion_keywords:
                if keyword.lower() in text_lower:
                    exclusion_found.append(keyword)
            
            info['injection_keywords_found'] = injection_found
            info['exclusion_keywords_found'] = exclusion_found
            
            # 判断是否为注射剂
            drug_name = info.get('title', '').lower()

            # 1. 药品名称中包含明确的注射剂词汇
            strong_injection_indicators = ['injection', 'injectable', 'intravenous', 'intramuscular', 'subcutaneous']
            name_has_strong_injection = any(keyword in drug_name for keyword in strong_injection_indicators)

            # 2. 药品名称中包含可能的注射剂词汇
            weak_injection_indicators = ['solution', 'vial', 'ampule', 'ampoule']
            name_has_weak_injection = any(keyword in drug_name for keyword in weak_injection_indicators)

            # 3. 药品名称中包含明显的非注射剂词汇
            strong_exclusion_indicators = ['tablet', 'capsule', 'cream', 'ointment', 'gel', 'lotion', 'syrup', 'liquid']
            name_has_exclusion = any(keyword in drug_name for keyword in strong_exclusion_indicators)

            # 4. 内容中有注射剂关键词
            has_injection_keywords = len(injection_found) > 0

            # 综合判断逻辑：
            # - 如果名称有强注射剂指示词，且名称没有排除词 -> 是注射剂
            # - 如果名称有弱注射剂指示词，且名称没有排除词，且内容有注射剂关键词 -> 是注射剂
            # - 如果只有内容关键词，但名称有排除词 -> 不是注射剂

            if name_has_strong_injection and not name_has_exclusion:
                info['is_injection'] = True
            elif name_has_weak_injection and not name_has_exclusion and has_injection_keywords:
                info['is_injection'] = True
            else:
                info['is_injection'] = False

            # 添加调试信息
            info['name_has_strong_injection'] = name_has_strong_injection
            info['name_has_weak_injection'] = name_has_weak_injection
            info['name_has_exclusion'] = name_has_exclusion
            info['has_injection_keywords'] = has_injection_keywords
            
            return info
            
        except Exception as e:
            logger.error(f"获取药品详情失败 (SETID: {setid}): {e}")
            return None
    
    def search_and_filter_injections(self, search_type="INACTIVE_INGREDIENT", 
                                   search_term="ALCOHOL", label_type="human",
                                   max_pages=None, max_drugs=None, 
                                   output_file=None, delay=0.5):
        """搜索并过滤注射剂"""
        logger.info(f"开始搜索: {search_type}:({search_term})")
        logger.info(f"过滤条件: 注射剂")
        
        all_drugs = []
        injection_drugs = []
        total_checked = 0
        page = 1
        
        # 首先获取总数
        _, total_count = self.search_drugs(search_type, search_term, label_type, page=1, pagesize=50)
        logger.info(f"搜索到总计 {total_count} 个药品")
        
        if max_pages is None:
            max_pages = (total_count // 50) + 1
        
        if max_drugs is None:
            max_drugs = total_count
        
        while page <= max_pages and total_checked < max_drugs:
            logger.info(f"正在搜索第 {page} 页...")
            search_results, _ = self.search_drugs(search_type, search_term, label_type, 
                                                page=page, pagesize=50)
            
            if not search_results:
                logger.warning(f"第 {page} 页没有找到结果")
                break
            
            for drug in search_results:
                if total_checked >= max_drugs:
                    break
                
                total_checked += 1
                logger.info(f"检查药品 {total_checked}/{min(max_drugs, total_count)}: {drug['name']}")
                
                # 获取药品详情
                drug_details = self.get_drug_details(drug['setid'])
                
                if drug_details:
                    drug_info = {
                        'name': drug['name'],
                        'setid': drug['setid'],
                        'url': drug['url'],
                        'title': drug_details.get('title', ''),
                        'dosage_form': drug_details.get('dosage_form', ''),
                        'is_injection': drug_details.get('is_injection', False),
                        'injection_keywords': ', '.join(drug_details.get('injection_keywords_found', [])),
                        'exclusion_keywords': ', '.join(drug_details.get('exclusion_keywords_found', []))
                    }
                    
                    all_drugs.append(drug_info)
                    
                    if drug_details.get('is_injection', False):
                        injection_drugs.append(drug_info)
                        logger.info(f"✅ 发现注射剂: {drug['name']}")
                        if drug_details.get('injection_keywords_found'):
                            logger.info(f"   关键词: {drug_details['injection_keywords_found']}")
                    else:
                        # 调试信息：为什么不是注射剂
                        logger.debug(f"❌ 非注射剂: {drug['name']}")
                        logger.debug(f"   名称包含强注射剂词汇: {drug_details.get('name_has_strong_injection', False)}")
                        logger.debug(f"   名称包含弱注射剂词汇: {drug_details.get('name_has_weak_injection', False)}")
                        logger.debug(f"   名称包含排除词汇: {drug_details.get('name_has_exclusion', False)}")
                        logger.debug(f"   内容包含注射剂关键词: {drug_details.get('has_injection_keywords', False)}")
                        if drug_details.get('injection_keywords_found'):
                            logger.debug(f"   找到的注射剂关键词: {drug_details['injection_keywords_found']}")
                        if drug_details.get('exclusion_keywords_found'):
                            logger.debug(f"   找到的排除关键词: {drug_details['exclusion_keywords_found']}")
                
                # 添加延迟避免请求过快
                time.sleep(delay)
            
            page += 1
        
        logger.info(f"搜索完成！")
        logger.info(f"总共检查了 {total_checked} 个药品")
        logger.info(f"找到 {len(injection_drugs)} 个注射剂")
        
        # 保存结果
        if output_file:
            self.save_results(injection_drugs, output_file)
        
        return injection_drugs, all_drugs
    
    def save_results(self, results, filename):
        """保存结果到CSV文件"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                if not results:
                    logger.warning("没有结果可保存")
                    return
                
                fieldnames = ['name', 'setid', 'url', 'title', 'dosage_form', 
                            'is_injection', 'injection_keywords', 'exclusion_keywords']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in results:
                    writer.writerow(result)
                
                logger.info(f"结果已保存到: {filename}")
                
        except Exception as e:
            logger.error(f"保存文件失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='DailyMed高级搜索和注射剂过滤工具')
    parser.add_argument('--search-type', default='INACTIVE_INGREDIENT', 
                       help='搜索类型 (默认: INACTIVE_INGREDIENT)')
    parser.add_argument('--search-term', default='ALCOHOL', 
                       help='搜索词 (默认: ALCOHOL)')
    parser.add_argument('--label-type', default='human', 
                       help='标签类型 (默认: human)')
    parser.add_argument('--max-pages', type=int, 
                       help='最大搜索页数')
    parser.add_argument('--max-drugs', type=int, 
                       help='最大检查药品数')
    parser.add_argument('--output', 
                       help='输出CSV文件名')
    parser.add_argument('--delay', type=float, default=0.5, 
                       help='请求间隔时间(秒) (默认: 0.5)')
    
    args = parser.parse_args()
    
    # 生成默认输出文件名
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output = f"dailymed_injections_{args.search_term}_{timestamp}.csv"
    
    searcher = DailyMedAdvancedSearcher()
    
    logger.info("=== DailyMed高级搜索开始 ===")
    
    injection_drugs, all_drugs = searcher.search_and_filter_injections(
        search_type=args.search_type,
        search_term=args.search_term,
        label_type=args.label_type,
        max_pages=args.max_pages,
        max_drugs=args.max_drugs,
        output_file=args.output,
        delay=args.delay
    )
    
    # 显示结果摘要
    if injection_drugs:
        logger.info(f"\n=== 找到的注射剂 ({len(injection_drugs)}个) ===")
        for i, drug in enumerate(injection_drugs[:10], 1):  # 只显示前10个
            logger.info(f"{i}. {drug['name']}")
            if drug['dosage_form']:
                logger.info(f"   剂型: {drug['dosage_form']}")
            if drug['injection_keywords']:
                logger.info(f"   关键词: {drug['injection_keywords']}")
        
        if len(injection_drugs) > 10:
            logger.info(f"... 还有 {len(injection_drugs) - 10} 个结果")
    
    logger.info("\n=== 搜索完成 ===")

if __name__ == "__main__":
    main()
