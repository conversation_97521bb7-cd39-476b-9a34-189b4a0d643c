#!/usr/bin/env python3
"""
DailyMed增强版搜索脚本
重点检查DOSAGE AND ADMINISTRATION部分，下载XML详情，验证搜索词
"""

import urllib.request
import urllib.parse
import json
import time
import re
import html
import logging
import csv
import argparse
import xml.etree.ElementTree as ET
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DailyMedEnhancedSearcher:
    def __init__(self):
        self.base_api_url = "https://dailymed.nlm.nih.gov/dailymed/services/v2"
        self.base_web_url = "https://dailymed.nlm.nih.gov/dailymed"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # 注射剂相关关键词（用于DOSAGE AND ADMINISTRATION部分）
        self.injection_keywords = [
            # 给药途径
            'intravenous', 'intramuscular', 'subcutaneous', 'intravenously', 'intramuscularly',
            'iv', 'im', 'sc', 'sq', 'inject', 'injection', 'injectable',
            
            # 给药方式
            'bolus', 'infusion', 'infuse', 'parenteral', 'parenterally',
            
            # 包装和器具
            'vial', 'ampule', 'ampoule', 'syringe', 'needle',
            'single-dose vial', 'multi-dose vial', 'prefilled syringe',
            
            # 制剂描述
            'sterile solution', 'sterile suspension', 'solution for injection',
            'suspension for injection', 'powder for injection', 'concentrate for injection',
            
            # 给药指导
            'administer intravenously', 'administer intramuscularly', 
            'administer subcutaneously', 'administer by injection'
        ]
    
    def _make_request(self, url, accept_json=False):
        """发送HTTP请求"""
        headers = self.headers.copy()
        if accept_json:
            headers['Accept'] = 'application/json'
        
        req = urllib.request.Request(url, headers=headers)
        try:
            with urllib.request.urlopen(req, timeout=30) as response:
                return response.read().decode('utf-8')
        except Exception as e:
            logger.error(f"请求失败 {url}: {e}")
            return None
    
    def build_search_url(self, search_type="INACTIVE_INGREDIENT", search_term="ALCOHOL", 
                        label_type="human", page=1, pagesize=50):
        """构建高级搜索URL"""
        if search_type and search_term:
            query = f"{search_type}:({search_term})"
        else:
            query = search_term
        
        search_params = {
            'adv': '1',
            'labeltype': label_type,
            'query': query,
            'page': page,
            'pagesize': pagesize
        }
        
        url = f"{self.base_web_url}/search.cfm?" + urllib.parse.urlencode(search_params)
        return url
    
    def search_drugs(self, search_type="INACTIVE_INGREDIENT", search_term="ALCOHOL", 
                    label_type="human", page=1, pagesize=50):
        """执行高级搜索"""
        try:
            url = self.build_search_url(search_type, search_term, label_type, page, pagesize)
            logger.info(f"搜索URL: {url}")
            
            response_text = self._make_request(url)
            if not response_text:
                return [], 0
            
            # 解析搜索结果
            results = []
            
            # 查找包含setid的链接
            setid_pattern = r'/dailymed/drugInfo\.cfm\?setid=([a-f0-9-]+)'
            setid_matches = re.findall(setid_pattern, response_text)
            
            # 查找药品名称
            name_pattern = r'<a[^>]*href="[^"]*setid=[a-f0-9-]+"[^>]*>([^<]+)</a>'
            name_matches = re.findall(name_pattern, response_text, re.IGNORECASE)
            
            # 组合结果
            for i, setid in enumerate(setid_matches):
                name = html.unescape(name_matches[i].strip()) if i < len(name_matches) else f"Drug_{i+1}"
                results.append({
                    'name': name,
                    'setid': setid,
                    'url': f"{self.base_web_url}/drugInfo.cfm?setid={setid}"
                })
            
            # 获取总结果数
            total_results = 0
            patterns = [
                r'(\d+)\s+results',
                r'of\s+(\d+)\s+',
                r'(\d+)\s+of\s+\d+',
                r'first\s+\d+\s+of\s+(\d+)'
            ]
            
            for pattern in patterns:
                result_match = re.search(pattern, response_text, re.IGNORECASE)
                if result_match:
                    total_results = int(result_match.group(1))
                    break
            
            logger.info(f"页面 {page}: 找到 {len(results)} 个药品，总计 {total_results} 个结果")
            return results, total_results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return [], 0
    
    def download_drug_xml(self, setid):
        """下载药品的XML详情"""
        try:
            # DailyMed的XML下载URL
            xml_url = f"{self.base_web_url}/downloadzipfile.cfm?setId={setid}"
            
            logger.debug(f"下载XML: {xml_url}")
            
            # 注意：这里实际上下载的是ZIP文件，包含XML
            # 为了简化，我们直接获取网页版的详细信息
            detail_url = f"{self.base_web_url}/drugInfo.cfm?setid={setid}"
            response_text = self._make_request(detail_url)
            
            if response_text:
                return response_text
            else:
                return None
                
        except Exception as e:
            logger.error(f"下载XML失败 (SETID: {setid}): {e}")
            return None
    
    def extract_dosage_administration(self, html_content):
        """提取DOSAGE AND ADMINISTRATION部分"""
        if not html_content:
            return ""
        
        # 查找DOSAGE AND ADMINISTRATION部分
        patterns = [
            r'DOSAGE\s+AND\s+ADMINISTRATION(.*?)(?=<h[1-6]|$)',
            r'dosage\s+and\s+administration(.*?)(?=<h[1-6]|$)',
            r'ADMINISTRATION\s+AND\s+DOSAGE(.*?)(?=<h[1-6]|$)',
            r'administration\s+and\s+dosage(.*?)(?=<h[1-6]|$)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, html_content, re.IGNORECASE | re.DOTALL)
            if match:
                # 清理HTML标签
                content = re.sub(r'<[^>]+>', ' ', match.group(1))
                # 清理多余空白
                content = re.sub(r'\s+', ' ', content).strip()
                return content[:2000]  # 限制长度
        
        return ""
    
    def verify_search_term_in_content(self, html_content, search_term):
        """在药品详情中验证搜索词是否存在"""
        if not html_content or not search_term:
            return False, []
        
        # 清理HTML并转换为小写
        clean_content = re.sub(r'<[^>]+>', ' ', html_content).lower()
        search_terms = search_term.lower().split()
        
        found_terms = []
        for term in search_terms:
            if term in clean_content:
                found_terms.append(term)
        
        # 如果找到了所有搜索词，认为验证通过
        verification_passed = len(found_terms) == len(search_terms)
        
        return verification_passed, found_terms
    
    def analyze_injection_indicators(self, dosage_admin_text, full_content, drug_name=""):
        """分析注射剂指标"""
        if not dosage_admin_text and not full_content:
            return False, []

        # 首先检查药品名称中的明显排除词
        drug_name_lower = drug_name.lower()
        strong_exclusions = ['tablet', 'capsule', 'cream', 'ointment', 'gel', 'lotion',
                           'syrup', 'liquid', 'powder', 'suspension', 'drops', 'spray']

        # 如果药品名称包含明显的非注射剂词汇，需要更严格的判断
        has_exclusion_in_name = any(excl in drug_name_lower for excl in strong_exclusions)

        # 强注射剂指示词（在药品名称或DOSAGE部分出现）
        strong_injection_words = ['injection', 'injectable', 'intravenous', 'intramuscular',
                                'subcutaneous', 'parenteral']

        # 检查药品名称中的强注射剂指示词
        name_has_strong_injection = any(word in drug_name_lower for word in strong_injection_words)

        # 分析DOSAGE AND ADMINISTRATION部分
        dosage_keywords = []
        if dosage_admin_text:
            dosage_text_lower = dosage_admin_text.lower()
            for keyword in self.injection_keywords:
                if keyword.lower() in dosage_text_lower:
                    dosage_keywords.append(keyword)

        # 分析完整内容
        analysis_text = (dosage_admin_text + " " + (full_content or "")).lower()
        found_keywords = []
        for keyword in self.injection_keywords:
            if keyword.lower() in analysis_text:
                found_keywords.append(keyword)

        # 检查DOSAGE部分是否明确提到口服给药
        oral_indicators = ['oral', 'orally', 'by mouth', 'swallow', 'teaspoonful', 'tablespoonful']
        dosage_has_oral = any(word in dosage_admin_text.lower() for word in oral_indicators) if dosage_admin_text else False

        # 判断逻辑：
        # 1. 如果药品名称有强注射剂词汇 -> 是注射剂
        # 2. 如果DOSAGE部分明确提到口服给药 -> 不是注射剂
        # 3. 如果药品名称有排除词汇，但DOSAGE部分有明确的注射剂证据 -> 可能是注射剂
        # 4. 如果药品名称无排除词汇，且DOSAGE部分有注射剂关键词 -> 是注射剂

        if name_has_strong_injection:
            is_injection = True
        elif dosage_has_oral:
            # 如果DOSAGE部分明确提到口服，则不是注射剂
            is_injection = False
        elif has_exclusion_in_name:
            # 对于有排除词的药品，需要在DOSAGE部分找到明确的注射剂证据
            strong_dosage_evidence = any(word in dosage_admin_text.lower()
                                       for word in strong_injection_words) if dosage_admin_text else False
            is_injection = strong_dosage_evidence and len(dosage_keywords) >= 3
        else:
            # 对于没有排除词的药品，主要看DOSAGE部分的证据
            dosage_has_strong_injection = any(word in dosage_admin_text.lower()
                                            for word in strong_injection_words) if dosage_admin_text else False
            is_injection = dosage_has_strong_injection or len(dosage_keywords) >= 3

        return is_injection, found_keywords
    
    def get_enhanced_drug_details(self, setid, search_term):
        """获取增强的药品详细信息"""
        try:
            # 下载药品详情
            html_content = self.download_drug_xml(setid)
            if not html_content:
                return None
            
            info = {'setid': setid}
            
            # 提取基本信息
            title_match = re.search(r'<title>([^<]+)</title>', html_content, re.IGNORECASE)
            if title_match:
                info['title'] = html.unescape(title_match.group(1).strip())
            
            # 提取DOSAGE AND ADMINISTRATION部分
            dosage_admin = self.extract_dosage_administration(html_content)
            info['dosage_administration'] = dosage_admin
            
            # 验证搜索词是否在内容中
            verification_passed, found_terms = self.verify_search_term_in_content(html_content, search_term)
            info['search_term_verified'] = verification_passed
            info['found_search_terms'] = found_terms
            
            # 分析注射剂指标
            drug_name = info.get('title', '')
            is_injection, injection_keywords = self.analyze_injection_indicators(dosage_admin, html_content, drug_name)
            info['is_injection'] = is_injection
            info['injection_keywords_found'] = injection_keywords
            
            # 如果在DOSAGE AND ADMINISTRATION中找到注射剂关键词，标记为高置信度
            dosage_has_injection = any(keyword.lower() in dosage_admin.lower() for keyword in self.injection_keywords) if dosage_admin else False
            info['dosage_has_injection_keywords'] = dosage_has_injection
            
            return info
            
        except Exception as e:
            logger.error(f"获取增强药品详情失败 (SETID: {setid}): {e}")
            return None
    
    def search_and_filter_enhanced(self, search_type="INACTIVE_INGREDIENT", 
                                 search_term="ALCOHOL", label_type="human",
                                 max_pages=None, max_drugs=None, 
                                 output_file=None, delay=0.5,
                                 verify_search_term=True):
        """增强版搜索和过滤"""
        logger.info(f"开始增强搜索: {search_type}:({search_term})")
        logger.info(f"过滤条件: 注射剂 (重点检查DOSAGE AND ADMINISTRATION)")
        logger.info(f"搜索词验证: {'启用' if verify_search_term else '禁用'}")
        
        all_drugs = []
        injection_drugs = []
        total_checked = 0
        page = 1
        
        # 首先获取总数
        _, total_count = self.search_drugs(search_type, search_term, label_type, page=1, pagesize=50)
        logger.info(f"搜索到总计 {total_count} 个药品")
        
        if max_pages is None:
            max_pages = (total_count // 50) + 1
        
        if max_drugs is None:
            max_drugs = total_count
        
        while page <= max_pages and total_checked < max_drugs:
            logger.info(f"正在搜索第 {page} 页...")
            search_results, _ = self.search_drugs(search_type, search_term, label_type, 
                                                page=page, pagesize=50)
            
            if not search_results:
                logger.warning(f"第 {page} 页没有找到结果")
                break
            
            for drug in search_results:
                if total_checked >= max_drugs:
                    break
                
                total_checked += 1
                logger.info(f"检查药品 {total_checked}/{min(max_drugs, total_count)}: {drug['name']}")
                
                # 获取增强的药品详情
                drug_details = self.get_enhanced_drug_details(drug['setid'], search_term)
                
                if drug_details:
                    # 如果启用搜索词验证，且验证失败，则跳过
                    if verify_search_term and not drug_details.get('search_term_verified', False):
                        logger.warning(f"⚠️  搜索词验证失败: {drug['name']}")
                        logger.warning(f"   未找到搜索词: {search_term}")
                        continue
                    
                    drug_info = {
                        'name': drug['name'],
                        'setid': drug['setid'],
                        'url': drug['url'],
                        'title': drug_details.get('title', ''),
                        'dosage_administration': drug_details.get('dosage_administration', ''),
                        'is_injection': drug_details.get('is_injection', False),
                        'dosage_has_injection_keywords': drug_details.get('dosage_has_injection_keywords', False),
                        'injection_keywords': ', '.join(drug_details.get('injection_keywords_found', [])),
                        'search_term_verified': drug_details.get('search_term_verified', False),
                        'found_search_terms': ', '.join(drug_details.get('found_search_terms', []))
                    }
                    
                    all_drugs.append(drug_info)
                    
                    if drug_details.get('is_injection', False):
                        injection_drugs.append(drug_info)
                        confidence = "高" if drug_details.get('dosage_has_injection_keywords', False) else "中"
                        logger.info(f"✅ 发现注射剂 (置信度: {confidence}): {drug['name']}")
                        
                        if drug_details.get('dosage_administration'):
                            logger.info(f"   DOSAGE & ADMIN: {drug_details['dosage_administration'][:100]}...")
                        
                        if drug_details.get('injection_keywords_found'):
                            logger.info(f"   关键词: {drug_details['injection_keywords_found'][:5]}")
                
                # 添加延迟避免请求过快
                time.sleep(delay)
            
            page += 1
        
        logger.info(f"搜索完成！")
        logger.info(f"总共检查了 {total_checked} 个药品")
        logger.info(f"找到 {len(injection_drugs)} 个注射剂")
        
        # 保存结果
        if output_file:
            self.save_enhanced_results(injection_drugs, output_file)
        
        return injection_drugs, all_drugs
    
    def save_enhanced_results(self, results, filename):
        """保存增强结果到CSV文件"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                if not results:
                    logger.warning("没有结果可保存")
                    return
                
                fieldnames = [
                    'name', 'setid', 'url', 'title', 
                    'dosage_administration', 'is_injection', 
                    'dosage_has_injection_keywords', 'injection_keywords',
                    'search_term_verified', 'found_search_terms'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in results:
                    writer.writerow(result)
                
                logger.info(f"结果已保存到: {filename}")
                
        except Exception as e:
            logger.error(f"保存文件失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='DailyMed增强版搜索和注射剂过滤工具')
    parser.add_argument('--search-type', default='INACTIVE_INGREDIENT', 
                       help='搜索类型 (默认: INACTIVE_INGREDIENT)')
    parser.add_argument('--search-term', default='ALCOHOL', 
                       help='搜索词 (默认: ALCOHOL)')
    parser.add_argument('--label-type', default='human', 
                       help='标签类型 (默认: human)')
    parser.add_argument('--max-pages', type=int, 
                       help='最大搜索页数')
    parser.add_argument('--max-drugs', type=int, 
                       help='最大检查药品数')
    parser.add_argument('--output', 
                       help='输出CSV文件名')
    parser.add_argument('--delay', type=float, default=0.5, 
                       help='请求间隔时间(秒) (默认: 0.5)')
    parser.add_argument('--no-verify', action='store_true',
                       help='禁用搜索词验证')
    
    args = parser.parse_args()
    
    # 生成默认输出文件名
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output = f"enhanced_injections_{args.search_term}_{timestamp}.csv"
    
    searcher = DailyMedEnhancedSearcher()
    
    logger.info("=== DailyMed增强版搜索开始 ===")
    
    injection_drugs, all_drugs = searcher.search_and_filter_enhanced(
        search_type=args.search_type,
        search_term=args.search_term,
        label_type=args.label_type,
        max_pages=args.max_pages,
        max_drugs=args.max_drugs,
        output_file=args.output,
        delay=args.delay,
        verify_search_term=not args.no_verify
    )
    
    # 显示结果摘要
    if injection_drugs:
        logger.info(f"\n=== 找到的注射剂 ({len(injection_drugs)}个) ===")
        for i, drug in enumerate(injection_drugs[:5], 1):  # 只显示前5个
            confidence = "高" if drug['dosage_has_injection_keywords'] else "中"
            logger.info(f"{i}. {drug['name']} (置信度: {confidence})")
            if drug['dosage_administration']:
                logger.info(f"   DOSAGE & ADMIN: {drug['dosage_administration'][:100]}...")
            if drug['injection_keywords']:
                logger.info(f"   关键词: {drug['injection_keywords']}")
        
        if len(injection_drugs) > 5:
            logger.info(f"... 还有 {len(injection_drugs) - 5} 个结果")
    
    logger.info("\n=== 搜索完成 ===")

if __name__ == "__main__":
    main()
