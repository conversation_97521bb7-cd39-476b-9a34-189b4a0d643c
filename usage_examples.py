#!/usr/bin/env python3
"""
DailyMed高级搜索工具使用示例
"""

import subprocess
import sys
from datetime import datetime

def run_search(description, command):
    """运行搜索命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"示例: {description}")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print("✅ 执行成功")
            # 显示最后几行输出
            lines = result.stdout.strip().split('\n')
            for line in lines[-10:]:
                if any(keyword in line for keyword in ['找到', '发现注射剂', '搜索完成', '结果已保存']):
                    print(line)
        else:
            print("❌ 执行失败")
            print(result.stderr)
    except subprocess.TimeoutExpired:
        print("⏰ 执行超时")
    except Exception as e:
        print(f"❌ 执行错误: {e}")

def main():
    print("DailyMed高级搜索工具使用示例")
    print("=" * 60)
    
    # 示例1: 搜索含酒精的注射剂
    run_search(
        "搜索含酒精的注射剂（前50个药品）",
        "python3 dailymed_advanced_search.py --search-type INACTIVE_INGREDIENT --search-term ALCOHOL --max-drugs 50 --output alcohol_injections.csv"
    )
    
    # 示例2: 搜索含苯甲醇的注射剂
    run_search(
        "搜索含苯甲醇的注射剂（前30个药品）",
        "python3 dailymed_advanced_search.py --search-type INACTIVE_INGREDIENT --search-term 'BENZYL ALCOHOL' --max-drugs 30 --output benzyl_alcohol_injections.csv"
    )
    
    # 示例3: 搜索含丙二醇的注射剂
    run_search(
        "搜索含丙二醇的注射剂（前30个药品）",
        "python3 dailymed_advanced_search.py --search-type INACTIVE_INGREDIENT --search-term 'PROPYLENE GLYCOL' --max-drugs 30 --output propylene_glycol_injections.csv"
    )
    
    # 示例4: 搜索含聚乙二醇的注射剂
    run_search(
        "搜索含聚乙二醇的注射剂（前30个药品）",
        "python3 dailymed_advanced_search.py --search-type INACTIVE_INGREDIENT --search-term 'POLYETHYLENE GLYCOL' --max-drugs 30 --output peg_injections.csv"
    )
    
    # 示例5: 搜索特定活性成分的注射剂
    run_search(
        "搜索含胰岛素的注射剂（前20个药品）",
        "python3 dailymed_advanced_search.py --search-type ACTIVE_INGREDIENT --search-term INSULIN --max-drugs 20 --output insulin_injections.csv"
    )
    
    print(f"\n{'='*60}")
    print("所有示例执行完成！")
    print("生成的CSV文件包含以下信息：")
    print("- name: 药品名称")
    print("- setid: 药品唯一标识符")
    print("- url: 药品详情页面URL")
    print("- title: 完整标题")
    print("- dosage_form: 剂型")
    print("- is_injection: 是否为注射剂")
    print("- injection_keywords: 找到的注射剂关键词")
    print("- exclusion_keywords: 找到的排除关键词")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
