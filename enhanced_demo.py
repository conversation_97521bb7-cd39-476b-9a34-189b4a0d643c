#!/usr/bin/env python3
"""
DailyMed增强版搜索工具演示脚本
展示重点检查DOSAGE AND ADMINISTRATION部分和搜索词验证功能
"""

import subprocess
import sys
import os
from datetime import datetime

def run_enhanced_search(search_term, max_drugs=50, search_type="INACTIVE_INGREDIENT"):
    """运行增强版搜索"""
    print(f"\n{'='*80}")
    print(f"🔍 增强搜索: {search_type}:({search_term}) 的注射剂（前{max_drugs}个药品）")
    print(f"✨ 特色功能:")
    print(f"   • 重点检查 DOSAGE AND ADMINISTRATION 部分")
    print(f"   • 验证搜索词在药品详情中的存在")
    print(f"   • 智能排除口服药品")
    print(f"   • 高精度注射剂识别")
    print(f"{'='*80}")
    
    # 生成输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"enhanced_{search_term.lower().replace(' ', '_')}_injections_{timestamp}.csv"
    
    # 构建命令
    command = [
        "python3", "dailymed_enhanced_search.py",
        "--search-type", search_type,
        "--search-term", search_term,
        "--max-drugs", str(max_drugs),
        "--output", output_file
    ]
    
    print(f"执行命令: {' '.join(command)}")
    print(f"输出文件: {output_file}")
    print("-" * 80)
    
    try:
        # 运行搜索
        result = subprocess.run(command, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            # 解析输出，提取关键信息
            lines = result.stdout.strip().split('\n')
            
            total_checked = 0
            total_found = 0
            high_confidence = 0
            
            for line in lines:
                if "总共检查了" in line and "个药品" in line:
                    try:
                        total_checked = int(line.split("总共检查了")[1].split("个药品")[0].strip())
                    except:
                        pass
                
                if "找到" in line and "个注射剂" in line:
                    try:
                        total_found = int(line.split("找到")[1].split("个注射剂")[0].strip())
                    except:
                        pass
                
                if "置信度: 高" in line:
                    high_confidence += 1
            
            print(f"✅ 搜索完成！")
            print(f"📊 检查了 {total_checked} 个药品")
            print(f"💉 找到 {total_found} 个注射剂")
            print(f"🎯 高置信度: {high_confidence} 个")
            
            # 显示找到的注射剂
            if total_found > 0:
                print(f"\n🎯 找到的注射剂:")
                in_results_section = False
                count = 0
                
                for line in lines:
                    if "=== 找到的注射剂" in line:
                        in_results_section = True
                        continue
                    elif "=== 搜索完成 ===" in line:
                        in_results_section = False
                        break
                    elif in_results_section and line.strip():
                        if line.strip().startswith(("1.", "2.", "3.", "4.", "5.")):
                            print(f"  {line.strip()}")
                            count += 1
                        elif "DOSAGE & ADMIN:" in line:
                            print(f"    {line.strip()}")
                        elif "关键词:" in line:
                            print(f"    {line.strip()}")
                        
                        if count >= 5:  # 只显示前5个
                            if total_found > 5:
                                print(f"    ... 还有 {total_found - 5} 个结果")
                            break
            
            # 检查文件是否生成
            if os.path.exists(output_file):
                print(f"\n📄 结果已保存到: {output_file}")
                return output_file, total_found
            else:
                print(f"\n⚠️  输出文件未生成")
                return None, 0
                
        else:
            print(f"❌ 搜索失败:")
            print(result.stderr)
            return None, 0
            
    except subprocess.TimeoutExpired:
        print("⏰ 搜索超时")
        return None, 0
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        return None, 0

def show_enhanced_csv_summary(csv_file):
    """显示增强版CSV文件摘要"""
    if not csv_file or not os.path.exists(csv_file):
        return
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) <= 1:
            print("📄 CSV文件为空")
            return
        
        print(f"\n📋 增强版CSV文件摘要 ({csv_file}):")
        print(f"   总记录数: {len(lines) - 1}")
        print(f"   文件大小: {os.path.getsize(csv_file)} 字节")
        
        # 分析CSV内容
        headers = lines[0].strip().split(',')
        print(f"   包含字段: {len(headers)} 个")
        
        # 显示关键字段
        key_fields = ['dosage_administration', 'dosage_has_injection_keywords', 'search_term_verified']
        for field in key_fields:
            if field in headers:
                print(f"   ✓ {field}")
        
        # 显示前几行
        print(f"\n📝 前3条记录:")
        for i, line in enumerate(lines[1:4], 1):  # 跳过标题行
            if line.strip():
                parts = line.split(',')
                if len(parts) >= 2:
                    name = parts[0].strip('"')
                    print(f"   {i}. {name}")
        
    except Exception as e:
        print(f"❌ 读取CSV文件失败: {e}")

def main():
    """主演示函数"""
    print("🧪 DailyMed增强版搜索工具演示")
    print("=" * 80)
    print("本演示展示增强版功能:")
    print("• 重点检查 DOSAGE AND ADMINISTRATION 部分")
    print("• 验证搜索词在药品详情中的存在")
    print("• 智能排除口服药品和外用药品")
    print("• 提供高精度的注射剂识别")
    print()
    
    # 检查脚本是否存在
    if not os.path.exists("dailymed_enhanced_search.py"):
        print("❌ 找不到 dailymed_enhanced_search.py 脚本")
        print("请确保该文件在当前目录中")
        return
    
    # 演示搜索列表
    demo_searches = [
        ("ALCOHOL", 50, "INACTIVE_INGREDIENT"),           # 酒精
        ("BENZYL ALCOHOL", 30, "INACTIVE_INGREDIENT"),    # 苯甲醇
        ("PROPYLENE GLYCOL", 30, "INACTIVE_INGREDIENT"),  # 丙二醇
        ("INSULIN", 20, "ACTIVE_INGREDIENT"),             # 胰岛素（活性成分）
    ]
    
    results = []
    total_injections = 0
    
    for search_term, max_drugs, search_type in demo_searches:
        csv_file, found_count = run_enhanced_search(search_term, max_drugs, search_type)
        if csv_file:
            results.append((search_term, csv_file, found_count))
            total_injections += found_count
            show_enhanced_csv_summary(csv_file)
        
        print()  # 空行分隔
    
    # 最终总结
    print("🎉 增强版演示完成！")
    print("=" * 80)
    
    if results:
        print("📁 生成的文件:")
        for search_term, csv_file, count in results:
            print(f"   • {search_term}: {csv_file} ({count} 个注射剂)")
        
        print(f"\n📊 总计找到 {total_injections} 个注射剂")
        
        print("\n💡 增强版特色:")
        print("   • CSV文件包含 DOSAGE AND ADMINISTRATION 完整内容")
        print("   • dosage_has_injection_keywords 字段显示DOSAGE部分是否包含注射剂关键词")
        print("   • search_term_verified 字段确认搜索词在药品详情中存在")
        print("   • injection_keywords 字段列出所有找到的注射剂关键词")
        print("   • 高精度过滤，减少误判")
        
        print("\n🔍 数据验证:")
        print("   • 所有结果都经过搜索词验证")
        print("   • 重点分析 DOSAGE AND ADMINISTRATION 部分")
        print("   • 智能排除口服药品（如含有'oral'、'orally'等词汇）")
        print("   • 区分高置信度和中置信度结果")
    else:
        print("⚠️  没有生成任何结果文件")
    
    print("\n📖 更多用法请参考:")
    print("   • README.md - 基础用法")
    print("   • dailymed_enhanced_search.py --help - 命令行参数")

if __name__ == "__main__":
    main()
