#!/usr/bin/env python3
"""
DailyMed药品数据爬取器
用于获取含酒精的注射剂药品信息
"""

import urllib.request
import urllib.parse
import json
import time
import re
import html
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DailyMedScraper:
    def __init__(self):
        self.base_api_url = "https://dailymed.nlm.nih.gov/dailymed/services/v2"
        self.base_web_url = "https://dailymed.nlm.nih.gov/dailymed"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # 注射剂相关关键词
        self.injection_keywords = [
            'injectable', 'injection', 'intravenous', 'intramuscular', 'subcutaneous',
            'solution for injection', 'suspension for injection', 'iv', 'im', 'sc',
            'vial', 'ampule', 'ampoule', 'prefilled syringe', 'auto-injector',
            'bolus', 'infusion', 'parenteral'
        ]
    
    def _make_request(self, url, accept_json=False):
        """发送HTTP请求"""
        headers = self.headers.copy()
        if accept_json:
            headers['Accept'] = 'application/json'

        req = urllib.request.Request(url, headers=headers)
        try:
            with urllib.request.urlopen(req, timeout=30) as response:
                return response.read().decode('utf-8')
        except Exception as e:
            logger.error(f"请求失败 {url}: {e}")
            return None

    def test_api_connection(self):
        """测试API连接"""
        try:
            url = f"{self.base_api_url}/spls.json"
            logger.info(f"测试API连接: {url}")

            response_text = self._make_request(url, accept_json=True)
            if not response_text:
                return False, None

            data = json.loads(response_text)
            logger.info(f"API连接成功，返回数据类型: {type(data)}")

            if isinstance(data, dict) and 'data' in data:
                logger.info(f"SPL数据条数: {len(data['data'])}")
                return True, data
            elif isinstance(data, list):
                logger.info(f"SPL数据条数: {len(data)}")
                return True, data
            else:
                logger.warning(f"未知的数据格式: {data}")
                return True, data

        except Exception as e:
            logger.error(f"API连接失败: {e}")
            return False, None
    
    def search_alcohol_drugs_web(self, page=1, per_page=50):
        """通过网页搜索含酒精的药品"""
        try:
            # 构建搜索URL
            search_params = {
                'adv': '1',
                'labeltype': 'human',
                'query': 'INACTIVE_INGREDIENT:(ALCOHOL)',
                'page': page,
                'pagesize': per_page
            }

            url = f"{self.base_web_url}/search.cfm?" + urllib.parse.urlencode(search_params)
            logger.info(f"搜索URL: {url}")

            response_text = self._make_request(url)
            if not response_text:
                return [], 0

            # 简单的HTML解析，查找药品链接
            results = []

            # 查找包含setid的链接
            setid_pattern = r'/dailymed/drugInfo\.cfm\?setid=([a-f0-9-]+)'
            setid_matches = re.findall(setid_pattern, response_text)

            # 查找药品名称（在链接中）
            name_pattern = r'<a[^>]*href="[^"]*setid=[a-f0-9-]+"[^>]*>([^<]+)</a>'
            name_matches = re.findall(name_pattern, response_text, re.IGNORECASE)

            # 组合结果
            for i, setid in enumerate(setid_matches):
                name = html.unescape(name_matches[i].strip()) if i < len(name_matches) else f"Drug_{i+1}"
                results.append({
                    'name': name,
                    'setid': setid,
                    'url': f"{self.base_web_url}/drugInfo.cfm?setid={setid}"
                })

            # 获取总结果数
            total_results = 0
            result_pattern = r'(\d+)\s+results'
            result_match = re.search(result_pattern, response_text)
            if result_match:
                total_results = int(result_match.group(1))

            logger.info(f"页面 {page}: 找到 {len(results)} 个药品，总计 {total_results} 个结果")
            return results, total_results

        except Exception as e:
            logger.error(f"网页搜索失败: {e}")
            return [], 0
    
    def get_drug_details_api(self, setid):
        """通过API获取药品详细信息"""
        try:
            url = f"{self.base_api_url}/spls/{setid}.json"
            response_text = self._make_request(url, accept_json=True)
            if not response_text:
                return None

            data = json.loads(response_text)
            return data

        except Exception as e:
            logger.error(f"获取药品详情失败 (SETID: {setid}): {e}")
            return None

    def get_drug_details_web(self, setid):
        """通过网页获取药品详细信息（备用方案）"""
        try:
            url = f"{self.base_web_url}/drugInfo.cfm?setid={setid}"
            response_text = self._make_request(url)
            if not response_text:
                return None

            # 简单提取一些关键信息
            info = {'setid': setid}

            # 提取药品名称
            title_match = re.search(r'<title>([^<]+)</title>', response_text, re.IGNORECASE)
            if title_match:
                info['title'] = html.unescape(title_match.group(1).strip())

            # 检查是否包含注射剂关键词
            text_lower = response_text.lower()
            injection_found = []
            for keyword in self.injection_keywords:
                if keyword.lower() in text_lower:
                    injection_found.append(keyword)

            info['injection_keywords_found'] = injection_found
            info['is_injection'] = len(injection_found) > 0

            return info

        except Exception as e:
            logger.error(f"获取药品网页详情失败 (SETID: {setid}): {e}")
            return None
    
    def is_injection_drug(self, drug_data):
        """判断是否为注射剂"""
        if not drug_data:
            return False
        
        # 将药品数据转换为字符串进行搜索
        drug_text = json.dumps(drug_data, ensure_ascii=False).lower()
        
        # 检查是否包含注射剂关键词
        for keyword in self.injection_keywords:
            if keyword.lower() in drug_text:
                logger.info(f"发现注射剂关键词: {keyword}")
                return True
        
        return False
    
    def extract_drug_info(self, drug_data):
        """提取药品关键信息"""
        if not drug_data:
            return None
        
        try:
            info = {
                'setid': drug_data.get('setid', ''),
                'title': drug_data.get('title', ''),
                'generic_name': drug_data.get('generic_name', ''),
                'labeler': drug_data.get('labeler', ''),
                'dosage_form': drug_data.get('dosage_form', ''),
                'route': drug_data.get('route', ''),
                'ndc_codes': drug_data.get('ndc_codes', []),
                'active_ingredients': drug_data.get('active_ingredients', []),
                'inactive_ingredients': drug_data.get('inactive_ingredients', [])
            }
            return info
        except Exception as e:
            logger.error(f"提取药品信息失败: {e}")
            return None

    def find_alcohol_injections(self, max_pages=5, max_drugs=50):
        """查找含酒精的注射剂药品"""
        logger.info(f"开始搜索含酒精的注射剂，最多检查 {max_pages} 页，{max_drugs} 个药品")

        all_results = []
        injection_drugs = []
        total_checked = 0

        for page in range(1, max_pages + 1):
            if total_checked >= max_drugs:
                break

            logger.info(f"正在搜索第 {page} 页...")
            search_results, total_count = self.search_alcohol_drugs_web(page=page, per_page=20)

            if not search_results:
                logger.warning(f"第 {page} 页没有找到结果")
                break

            for drug in search_results:
                if total_checked >= max_drugs:
                    break

                total_checked += 1
                logger.info(f"检查药品 {total_checked}: {drug['name']}")

                # 首先尝试API获取详情
                drug_details = self.get_drug_details_api(drug['setid'])

                # 如果API失败，使用网页方式
                if not drug_details:
                    drug_details = self.get_drug_details_web(drug['setid'])

                if drug_details:
                    # 判断是否为注射剂
                    is_injection = self.is_injection_drug(drug_details)

                    # 如果网页方式有is_injection字段，优先使用
                    if 'is_injection' in drug_details:
                        is_injection = drug_details['is_injection']

                    if is_injection:
                        injection_info = {
                            'name': drug['name'],
                            'setid': drug['setid'],
                            'url': drug['url'],
                            'details': drug_details
                        }
                        injection_drugs.append(injection_info)
                        logger.info(f"✅ 发现注射剂: {drug['name']}")

                        # 如果有找到的关键词，显示出来
                        if 'injection_keywords_found' in drug_details:
                            logger.info(f"   关键词: {drug_details['injection_keywords_found']}")

                # 添加延迟避免请求过快
                time.sleep(0.5)

        logger.info(f"搜索完成！总共检查了 {total_checked} 个药品，找到 {len(injection_drugs)} 个注射剂")
        return injection_drugs

def main():
    """主函数 - 测试可行性"""
    scraper = DailyMedScraper()
    
    logger.info("=== DailyMed 可行性测试开始 ===")
    
    # 1. 测试API连接
    logger.info("1. 测试API连接...")
    api_success, api_data = scraper.test_api_connection()
    
    if api_success:
        logger.info("✅ API连接成功")
        if api_data:
            # 显示前几个SPL的信息
            data_list = api_data.get('data', api_data) if isinstance(api_data, dict) else api_data
            if isinstance(data_list, list) and len(data_list) > 0:
                logger.info(f"示例SPL数据: {data_list[0]}")
    else:
        logger.error("❌ API连接失败")
    
    # 2. 测试网页搜索
    logger.info("\n2. 测试网页搜索含酒精药品...")
    search_results, total_count = scraper.search_alcohol_drugs_web(page=1, per_page=20)
    
    if search_results:
        logger.info(f"✅ 网页搜索成功，找到 {len(search_results)} 个结果")
        logger.info(f"总结果数: {total_count}")
        
        # 显示前几个搜索结果
        for i, result in enumerate(search_results[:3]):
            logger.info(f"药品 {i+1}: {result}")
    else:
        logger.error("❌ 网页搜索失败")
    
    # 3. 测试获取药品详情
    if search_results:
        logger.info("\n3. 测试获取药品详情...")
        test_setid = search_results[0].get('setid')
        if test_setid:
            # 测试API方式
            drug_details = scraper.get_drug_details_api(test_setid)
            if drug_details:
                logger.info("✅ API药品详情获取成功")
                is_injection = scraper.is_injection_drug(drug_details)
                logger.info(f"API方式 - 是否为注射剂: {is_injection}")
            else:
                logger.info("API方式失败，尝试网页方式...")

            # 测试网页方式
            web_details = scraper.get_drug_details_web(test_setid)
            if web_details:
                logger.info("✅ 网页药品详情获取成功")
                logger.info(f"网页方式 - 是否为注射剂: {web_details.get('is_injection', False)}")
                if web_details.get('injection_keywords_found'):
                    logger.info(f"找到的关键词: {web_details['injection_keywords_found']}")

    # 4. 测试完整搜索流程
    logger.info("\n4. 测试完整搜索流程（前10个药品）...")
    injection_drugs = scraper.find_alcohol_injections(max_pages=2, max_drugs=10)

    if injection_drugs:
        logger.info(f"✅ 找到 {len(injection_drugs)} 个含酒精的注射剂:")
        for i, drug in enumerate(injection_drugs, 1):
            logger.info(f"{i}. {drug['name']} (SETID: {drug['setid']})")
    else:
        logger.info("在前10个药品中未找到注射剂")

    logger.info("\n=== 可行性测试完成 ===")

if __name__ == "__main__":
    main()
